import { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Box,
  Drawer,
  AppBar,
  Toolbar,
  List,
  Typography,
  IconButton,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Avatar,
  Badge,
  useTheme,
  useMediaQuery,
  Container,
  Alert,
  CircularProgress,
  Chip,
  Card,
  CardContent,
  Grid,
  Paper,
  Fade,
  Zoom,
  Tooltip
} from '@mui/material';
import {
  Menu as MenuIcon,
  Dashboard,
  VideoLibrary,
  Group,
  PersonAdd,
  Help,
  Settings,
  Logout,
  Notifications,
  CloudUpload,
  Storage,
  Analytics,
  Security,
  Language,
  Brightness4,
  Brightness7
} from '@mui/icons-material';

// استيراد السياقات والخدمات
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';

// استيراد المكونات الفرعية
import CourseManagement from './admin/CourseManagement';
import StudentManagement from './admin/StudentManagement';
import EnrollmentManagement from './admin/EnrollmentManagement';
import FAQManagement from './admin/FAQManagement';
import AdminProfile from './admin/AdminProfile';
import SystemSettings from './admin/SystemSettings';
import DatabaseInitializer from './admin/DatabaseInitializer';
import FileUploadManager from './admin/FileUploadManager';



/**
 * مكون محتوى القائمة الجانبية
 */
const DrawerContent = ({ menuItems, selectedSection, onSectionChange, user, language, shouldUseAnimations }) => (
  <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
    {/* رأس القائمة */}
    <Box className="modern-drawer-header">
      <Typography
        variant="h6"
        className="arabic-text-elegant arabic-text-bold arabic-text-white"
        sx={{ mb: 1 }}
      >
        SKILLS WORLD ACADEMY
      </Typography>
      <Typography
        variant="body2"
        className="arabic-text arabic-text-white"
        sx={{ opacity: 0.9 }}
      >
        {language === 'ar' ? 'لوحة تحكم المدير المحسنة' : 'Enhanced Admin Dashboard'}
      </Typography>
    </Box>

    {/* قائمة العناصر */}
    <List sx={{ flexGrow: 1, px: 1, py: 2 }}>
      {menuItems.map((item, index) => (
        <Fade
          key={item.id}
          in={true}
          timeout={shouldUseAnimations ? 300 + (index * 100) : 0}
        >
          <ListItem disablePadding sx={{ mb: 1 }}>
            <ListItemButton
              selected={selectedSection === item.id}
              onClick={() => onSectionChange(item.id)}
              className={`modern-list-item ${selectedSection === item.id ? 'selected' : ''}`}
              sx={{
                minHeight: 56,
                mx: 1,
                background: selectedSection === item.id
                  ? `linear-gradient(135deg, ${item.color}20, ${item.color}10)`
                  : 'transparent'
              }}
            >
              <ListItemIcon
                sx={{
                  color: selectedSection === item.id ? item.color : 'text.secondary',
                  minWidth: 40
                }}
              >
                {item.icon}
              </ListItemIcon>
              <ListItemText
                primary={item.label}
                secondary={item.description}
                className="arabic-text"
                primaryTypographyProps={{
                  className: selectedSection === item.id
                    ? 'arabic-text-semibold arabic-text-base'
                    : 'arabic-text-medium arabic-text-base',
                  color: selectedSection === item.id ? item.color : 'text.primary'
                }}
                secondaryTypographyProps={{
                  className: 'arabic-text-regular arabic-text-sm',
                  color: 'text.secondary'
                }}
              />
              {selectedSection === item.id && (
                <Chip
                  size="small"
                  label="●"
                  sx={{
                    backgroundColor: item.color,
                    color: 'white',
                    minWidth: 8,
                    height: 8,
                    '& .MuiChip-label': { px: 0 }
                  }}
                />
              )}
            </ListItemButton>
          </ListItem>
        </Fade>
      ))}
    </List>

    {/* معلومات المستخدم */}
    <Box sx={{ p: 2, borderTop: '1px solid #e0e0e0' }}>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
        <Avatar
          sx={{
            bgcolor: '#667eea',
            width: 40,
            height: 40
          }}
        >
          {user?.name?.charAt(0) || 'A'}
        </Avatar>
        <Box sx={{ flexGrow: 1 }}>
          <Typography
            variant="body2"
            className="arabic-text-semibold arabic-text-primary"
          >
            {user?.name || (language === 'ar' ? 'المدير' : 'Admin')}
          </Typography>
          <Typography
            variant="caption"
            className="arabic-text-regular arabic-text-secondary"
          >
            {language === 'ar' ? 'مدير النظام' : 'System Administrator'}
          </Typography>
        </Box>
        <Chip
          size="small"
          label={language === 'ar' ? 'متصل' : 'Online'}
          color="success"
          variant="outlined"
        />
      </Box>
    </Box>
  </Box>
);

/**
 * مكون المحتوى الرئيسي
 */
const MainContent = ({ selectedSection, language, shouldUseAnimations }) => {
  const renderContent = () => {
    switch (selectedSection) {
      case 'dashboard':
        return <DashboardOverview language={language} />;
      case 'courses':
        return <CourseManagement />;
      case 'students':
        return <StudentManagement />;
      case 'enrollments':
        return <EnrollmentManagement />;
      case 'faqs':
        return <FAQManagement />;
      case 'file-upload':
        return <FileUploadManager />;
      case 'database-initializer':
        return <DatabaseInitializer />;
      case 'analytics':
        return <AnalyticsDashboard language={language} />;
      case 'profile':
        return <AdminProfile />;
      case 'system-settings':
        return <SystemSettings />;
      default:
        return <DashboardOverview language={language} />;
    }
  };

  return (
    <Fade in={true} timeout={shouldUseAnimations ? 500 : 0}>
      <Box>
        {renderContent()}
      </Box>
    </Fade>
  );
};

/**
 * مكون نظرة عامة على لوحة التحكم
 */
const DashboardOverview = ({ language }) => (
  <Box>
    <Typography variant="h4" sx={{ mb: 3, fontWeight: 'bold' }}>
      {language === 'ar' ? 'مرحباً بك في لوحة التحكم المحسنة' : 'Welcome to Enhanced Dashboard'}
    </Typography>

    <Grid container spacing={3}>
      {/* بطاقات الإحصائيات */}
      <Grid item xs={12} sm={6} md={3}>
        <Zoom in={true} timeout={500}>
          <Card className="modern-card modern-card-gradient modern-float">
            <CardContent>
              <Typography
                variant="h6"
                className="arabic-text-elegant arabic-text-white"
              >
                {language === 'ar' ? 'إجمالي الكورسات' : 'Total Courses'}
              </Typography>
              <Typography
                variant="h3"
                className="arabic-text-extrabold arabic-text-white"
                sx={{ mt: 1 }}
              >
                12
              </Typography>
            </CardContent>
          </Card>
        </Zoom>
      </Grid>

      <Grid item xs={12} sm={6} md={3}>
        <Card sx={{
          background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
          color: 'white',
          borderRadius: 3
        }}>
          <CardContent>
            <Typography variant="h6">
              {language === 'ar' ? 'إجمالي الطلاب' : 'Total Students'}
            </Typography>
            <Typography variant="h3" sx={{ fontWeight: 'bold' }}>
              156
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} sm={6} md={3}>
        <Card sx={{
          background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
          color: 'white',
          borderRadius: 3
        }}>
          <CardContent>
            <Typography variant="h6">
              {language === 'ar' ? 'التسجيلات النشطة' : 'Active Enrollments'}
            </Typography>
            <Typography variant="h3" sx={{ fontWeight: 'bold' }}>
              89
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} sm={6} md={3}>
        <Card sx={{
          background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
          color: 'white',
          borderRadius: 3
        }}>
          <CardContent>
            <Typography variant="h6">
              {language === 'ar' ? 'الشهادات الصادرة' : 'Certificates Issued'}
            </Typography>
            <Typography variant="h3" sx={{ fontWeight: 'bold' }}>
              34
            </Typography>
          </CardContent>
        </Card>
      </Grid>
    </Grid>

    {/* رسالة ترحيب */}
    <Paper sx={{ p: 3, mt: 3, borderRadius: 3 }}>
      <Typography variant="h6" sx={{ mb: 2 }}>
        {language === 'ar' ? 'مرحباً بك في النظام المحسن' : 'Welcome to the Enhanced System'}
      </Typography>
      <Typography variant="body1" color="text.secondary">
        {language === 'ar'
          ? 'تم تطوير هذه اللوحة بتقنيات حديثة لتوفير تجربة مستخدم ممتازة مع أداء محسن واستقرار عالي.'
          : 'This dashboard has been developed with modern technologies to provide an excellent user experience with improved performance and high stability.'
        }
      </Typography>
    </Paper>
  </Box>
);



/**
 * مكون التحليلات (سيتم تطويره لاحقاً)
 */
const AnalyticsDashboard = ({ language }) => (
  <Box>
    <Typography variant="h4" sx={{ mb: 3 }}>
      {language === 'ar' ? 'لوحة التحليلات' : 'Analytics Dashboard'}
    </Typography>
    <Paper sx={{ p: 3, borderRadius: 3 }}>
      <Typography variant="body1">
        {language === 'ar' ? 'سيتم تطوير لوحة التحليلات قريباً...' : 'Analytics dashboard coming soon...'}
      </Typography>
    </Paper>
  </Box>
);

/**
 * Hook للتخطيط المتجاوب المحسن
 */
const useResponsiveLayout = () => {
  const theme = useTheme();
  
  // تحديد أنواع الأجهزة بدقة
  const isMobile = useMediaQuery('(max-width: 767px)');
  const isTablet = useMediaQuery('(min-width: 768px) and (max-width: 1024px)');
  const isLaptop = useMediaQuery('(min-width: 1025px) and (max-width: 1440px)');
  const isDesktop = useMediaQuery('(min-width: 1441px)');
  
  // تحديد نوع التفاعل
  const isTouchDevice = useMediaQuery('(pointer: coarse)');
  const hasHover = useMediaQuery('(hover: hover) and (pointer: fine)');
  const prefersReducedMotion = useMediaQuery('(prefers-reduced-motion: reduce)');
  
  // حساب الإعدادات المتجاوبة
  const layoutConfig = useMemo(() => ({
    // معلومات الجهاز
    isMobile,
    isTablet,
    isLaptop,
    isDesktop,
    isTouchDevice,
    hasHover,
    prefersReducedMotion,
    
    // أحجام محسنة
    drawerWidth: isMobile ? 280 : isTablet ? 300 : isLaptop ? 320 : 340,
    appBarHeight: isMobile ? 56 : isTablet ? 64 : 68,
    touchTargetSize: isTouchDevice ? (isMobile ? 44 : 48) : 40,
    
    // إعدادات التخطيط
    isPermanentDrawer: isLaptop || isDesktop,
    isTemporaryDrawer: isMobile || isTablet,
    
    // تحسينات الأداء
    shouldUseTransitions: hasHover && !prefersReducedMotion,
    shouldUseAnimations: !prefersReducedMotion
  }), [isMobile, isTablet, isLaptop, isDesktop, isTouchDevice, hasHover, prefersReducedMotion]);
  
  return layoutConfig;
};

/**
 * Hook لإدارة حالة لوحة التحكم
 */
const useDashboardState = () => {
  const [state, setState] = useState({
    selectedSection: 'dashboard',
    mobileOpen: false,
    loading: false,
    error: null,
    systemStatus: 'ready',
    notifications: [],
    darkMode: false
  });
  
  const updateState = useCallback((updates) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);
  
  return [state, updateState];
};

/**
 * مكون لوحة تحكم المدير المحسنة
 */
const AdminDashboardNew = () => {
  const { user, logout } = useAuth();
  const { language, t, toggleLanguage } = useLanguage();
  const theme = useTheme();
  
  // استخدام الـ hooks المخصصة
  const layout = useResponsiveLayout();
  const [dashboardState, updateDashboardState] = useDashboardState();
  
  const {
    selectedSection,
    mobileOpen,
    loading,
    error,
    systemStatus,
    notifications,
    darkMode
  } = dashboardState;
  
  const {
    drawerWidth,
    appBarHeight,
    isPermanentDrawer,
    isTemporaryDrawer,
    shouldUseAnimations
  } = layout;
  
  // معالجات الأحداث
  const handleDrawerToggle = useCallback(() => {
    updateDashboardState({ mobileOpen: !mobileOpen });
  }, [mobileOpen, updateDashboardState]);
  
  const handleSectionChange = useCallback((sectionId) => {
    updateDashboardState({ selectedSection: sectionId });
    if (isTemporaryDrawer && mobileOpen) {
      updateDashboardState({ mobileOpen: false });
    }
  }, [isTemporaryDrawer, mobileOpen, updateDashboardState]);
  
  const handleLogout = useCallback(async () => {
    try {
      updateDashboardState({ loading: true });
      await logout();
    } catch (error) {
      updateDashboardState({ error: error.message, loading: false });
    }
  }, [logout, updateDashboardState]);
  
  const toggleDarkMode = useCallback(() => {
    updateDashboardState({ darkMode: !darkMode });
  }, [darkMode, updateDashboardState]);
  
  // قائمة عناصر القائمة الجانبية
  const menuItems = useMemo(() => [
    {
      id: 'dashboard',
      label: t('dashboard') || 'لوحة التحكم',
      icon: <Dashboard />,
      color: '#2196F3',
      description: language === 'ar' ? 'نظرة عامة على النظام' : 'System Overview'
    },
    {
      id: 'courses',
      label: t('courseManagement') || 'إدارة الكورسات',
      icon: <VideoLibrary />,
      color: '#4CAF50',
      description: language === 'ar' ? 'إضافة وإدارة الكورسات' : 'Add and manage courses'
    },
    {
      id: 'students',
      label: t('studentManagement') || 'إدارة الطلاب',
      icon: <Group />,
      color: '#FF9800',
      description: language === 'ar' ? 'إضافة وإدارة الطلاب' : 'Add and manage students'
    },
    {
      id: 'enrollments',
      label: t('enrollmentManagement') || 'إدارة التسجيلات',
      icon: <PersonAdd />,
      color: '#9C27B0',
      description: language === 'ar' ? 'تسجيل الطلاب في الكورسات' : 'Enroll students in courses'
    },
    {
      id: 'faqs',
      label: language === 'ar' ? 'الأسئلة الشائعة' : 'FAQ Management',
      icon: <Help />,
      color: '#00BCD4',
      description: language === 'ar' ? 'إدارة الأسئلة الشائعة' : 'Manage frequently asked questions'
    },
    {
      id: 'file-upload',
      label: language === 'ar' ? 'رفع الملفات' : 'File Upload',
      icon: <CloudUpload />,
      color: '#E91E63',
      description: language === 'ar' ? 'رفع الفيديوهات وملفات PDF' : 'Upload videos and PDF files'
    },
    {
      id: 'database-initializer',
      label: language === 'ar' ? 'تهيئة قاعدة البيانات' : 'Database Setup',
      icon: <Storage />,
      color: '#9C27B0',
      description: language === 'ar' ? 'إعداد هيكل قاعدة البيانات' : 'Setup database structure'
    },
    {
      id: 'analytics',
      label: language === 'ar' ? 'التحليلات' : 'Analytics',
      icon: <Analytics />,
      color: '#607D8B',
      description: language === 'ar' ? 'إحصائيات وتقارير النظام' : 'System statistics and reports'
    },
    {
      id: 'profile',
      label: language === 'ar' ? 'الملف الشخصي' : 'Profile',
      icon: <Security />,
      color: '#795548',
      description: language === 'ar' ? 'إعدادات الملف الشخصي' : 'Profile settings'
    },
    {
      id: 'system-settings',
      label: t('systemSettings') || 'إعدادات النظام',
      icon: <Settings />,
      color: '#795548',
      description: language === 'ar' ? 'إعدادات النظام العامة' : 'General system settings'
    }
  ], [language, t]);
  
  // تأثيرات جانبية
  useEffect(() => {
    // تهيئة النظام عند التحميل
    const initializeSystem = async () => {
      try {
        updateDashboardState({ loading: true, systemStatus: 'initializing' });
        
        // محاكاة تحميل البيانات
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        updateDashboardState({ 
          loading: false, 
          systemStatus: 'ready',
          notifications: [
            {
              id: 1,
              type: 'success',
              message: language === 'ar' ? 'تم تحميل النظام بنجاح' : 'System loaded successfully'
            }
          ]
        });
      } catch (error) {
        updateDashboardState({ 
          loading: false, 
          error: error.message,
          systemStatus: 'error'
        });
      }
    };
    
    initializeSystem();
  }, [language, updateDashboardState]);
  
  // عرض شاشة التحميل
  if (loading && systemStatus === 'initializing') {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
        }}
      >
        <Card sx={{ p: 4, textAlign: 'center', borderRadius: 3 }}>
          <CircularProgress size={60} sx={{ mb: 2 }} />
          <Typography variant="h6" sx={{ mb: 1 }}>
            {language === 'ar' ? 'جاري تحميل لوحة التحكم...' : 'Loading Dashboard...'}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {language === 'ar' ? 'يرجى الانتظار' : 'Please wait'}
          </Typography>
        </Card>
      </Box>
    );
  }
  
  return (
    <Box sx={{ display: 'flex', minHeight: '100vh', direction: language === 'ar' ? 'rtl' : 'ltr' }}>
      {/* الشريط العلوي */}
      <AppBar
        position="fixed"
        className="modern-appbar"
        sx={{
          width: isPermanentDrawer ? `calc(100% - ${drawerWidth}px)` : '100%',
          ml: isPermanentDrawer ? `${drawerWidth}px` : 0,
          zIndex: theme.zIndex.drawer + 1
        }}
      >
        <Toolbar sx={{ minHeight: appBarHeight }}>
          {/* زر القائمة للأجهزة المحمولة */}
          {isTemporaryDrawer && (
            <IconButton
              color="inherit"
              aria-label="open drawer"
              edge="start"
              onClick={handleDrawerToggle}
              sx={{ mr: 2 }}
            >
              <MenuIcon />
            </IconButton>
          )}
          
          {/* عنوان الصفحة */}
          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            {menuItems.find(item => item.id === selectedSection)?.label || 'لوحة التحكم'}
          </Typography>
          
          {/* أزرار الشريط العلوي */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {/* تبديل اللغة */}
            <Tooltip title={language === 'ar' ? 'English' : 'العربية'}>
              <IconButton color="inherit" onClick={toggleLanguage}>
                <Language />
              </IconButton>
            </Tooltip>
            
            {/* تبديل الوضع المظلم */}
            <Tooltip title={darkMode ? 'الوضع الفاتح' : 'الوضع المظلم'}>
              <IconButton color="inherit" onClick={toggleDarkMode}>
                {darkMode ? <Brightness7 /> : <Brightness4 />}
              </IconButton>
            </Tooltip>
            
            {/* الإشعارات */}
            <Tooltip title={language === 'ar' ? 'الإشعارات' : 'Notifications'}>
              <IconButton color="inherit">
                <Badge badgeContent={notifications.length} color="error">
                  <Notifications />
                </Badge>
              </IconButton>
            </Tooltip>
            
            {/* تسجيل الخروج */}
            <Tooltip title={language === 'ar' ? 'تسجيل الخروج' : 'Logout'}>
              <IconButton color="inherit" onClick={handleLogout}>
                <Logout />
              </IconButton>
            </Tooltip>
          </Box>
        </Toolbar>
      </AppBar>
      
      {/* القائمة الجانبية */}
      <Box
        component="nav"
        sx={{ width: { sm: drawerWidth }, flexShrink: { sm: 0 } }}
      >
        {/* الدرج المؤقت للأجهزة المحمولة */}
        {isTemporaryDrawer && (
          <Drawer
            variant="temporary"
            anchor={language === 'ar' ? 'right' : 'left'}
            open={mobileOpen}
            onClose={handleDrawerToggle}
            ModalProps={{ keepMounted: true }}
            sx={{
              '& .MuiDrawer-paper': {
                boxSizing: 'border-box',
                width: drawerWidth
              }
            }}
            PaperProps={{
              className: 'modern-drawer'
            }}
          >
            <DrawerContent
              menuItems={menuItems}
              selectedSection={selectedSection}
              onSectionChange={handleSectionChange}
              user={user}
              language={language}
              shouldUseAnimations={shouldUseAnimations}
            />
          </Drawer>
        )}
        
        {/* الدرج الثابت للشاشات الكبيرة */}
        {isPermanentDrawer && (
          <Drawer
            variant="permanent"
            sx={{
              '& .MuiDrawer-paper': {
                boxSizing: 'border-box',
                width: drawerWidth,
                borderRight: 'none'
              }
            }}
            PaperProps={{
              className: 'modern-drawer'
            }}
            open
          >
            <DrawerContent
              menuItems={menuItems}
              selectedSection={selectedSection}
              onSectionChange={handleSectionChange}
              user={user}
              language={language}
              shouldUseAnimations={shouldUseAnimations}
            />
          </Drawer>
        )}
      </Box>
      
      {/* المحتوى الرئيسي */}
      <Box
        component="main"
        className="modern-bg-primary"
        sx={{
          flexGrow: 1,
          p: 3,
          width: { sm: `calc(100% - ${isPermanentDrawer ? drawerWidth : 0}px)` },
          mt: `${appBarHeight}px`,
          minHeight: `calc(100vh - ${appBarHeight}px)`
        }}
      >
        {/* عرض رسائل الخطأ */}
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
        
        {/* محتوى الصفحة */}
        <Container maxWidth="xl">
          <MainContent
            selectedSection={selectedSection}
            language={language}
            shouldUseAnimations={shouldUseAnimations}
          />
        </Container>
      </Box>
    </Box>
  );
};

export default AdminDashboardNew;
