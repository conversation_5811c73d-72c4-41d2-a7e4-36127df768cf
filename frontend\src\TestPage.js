import React from 'react';
import { Box, Typography, Button } from '@mui/material';

const TestPage = () => {
  return (
    <Box 
      sx={{ 
        minHeight: '100vh', 
        display: 'flex', 
        flexDirection: 'column',
        alignItems: 'center', 
        justifyContent: 'center',
        bgcolor: '#f5f5f5',
        p: 3
      }}
    >
      <Typography variant="h3" component="h1" gutterBottom color="primary">
        🎓 SKILLS WORLD ACADEMY
      </Typography>
      
      <Typography variant="h5" component="h2" gutterBottom color="text.secondary">
        اختبار المشروع - الصفحة تعمل بنجاح!
      </Typography>
      
      <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
        <Button 
          variant="contained" 
          color="primary" 
          size="large"
          onClick={() => window.location.href = '/login'}
        >
          الذهاب لصفحة تسجيل الدخول
        </Button>
        
        <Button 
          variant="outlined" 
          color="secondary" 
          size="large"
          onClick={() => console.log('تم النقر على الزر!')}
        >
          اختبار الزر
        </Button>
      </Box>
      
      <Typography variant="body1" sx={{ mt: 3, textAlign: 'center' }}>
        إذا كنت ترى هذه الصفحة، فإن React و Material-UI يعملان بشكل صحيح
      </Typography>
    </Box>
  );
};

export default TestPage;
